/*
 * 版本: v2.0.0
 * 变更记录:
 * - v2.0.0 (2025-01-27): 重大重构 - 将HTML中的内联CSS移动到此文件，实现关注点分离，提升代码可维护性
 * - v1.2.0 (2025-01-27): 添加section最小高度，优化页面布局，修复底部空白问题
 * - v1.1.0 (2025-01-27): 更新所有背景色为新的浅灰蓝色主题 #d4e6f1，保持UI一致性
 * - v1.0.0: 初始版本，定义基础样式和卡片效果
 */

/* 从HTML移动过来的样式 */

/* 高级卡片样式 - v2.0 增强悬浮交互效果 */
.card {
    /* 升级阴影效果，使用多层阴影创造更精致的深度感 */
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.02),
        0 4px 16px rgba(0, 0, 0, 0.03),
        0 8px 32px rgba(79, 134, 198, 0.04),
        0 16px 64px rgba(79, 134, 198, 0.02),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    /* 改进过渡效果，使用更流畅的缓动曲线 */
    transition:
        transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
        box-shadow 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
        background-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
        border-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
        backdrop-filter 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    /* 增强边框效果 */
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(212, 230, 241, 0.18);
    backdrop-filter: blur(12px) saturate(1.1);
    position: relative;
    overflow: hidden;
    padding: 1rem;
    border-radius: 0.5rem;
    /* 提示浏览器该元素将发生变化 */
    will-change: transform, box-shadow, backdrop-filter;
}

/* 桌面端卡片增强 - 增加内边距使卡片更宽，优化水平布局高度 */
@media (min-width: 640px) {
    .card {
        padding: 1rem;
        min-height: 6rem;
    }
}

/* 移动端优化 - 降级悬浮效果 */
@media (max-width: 767px) {
    .card {
        padding: 0.75rem;
        border-radius: 0.375rem;
        min-height: 4.5rem;
        backdrop-filter: blur(8px) saturate(1.05);
        background: rgba(212, 230, 241, 0.15);
        box-shadow:
            0 2px 6px rgba(0, 0, 0, 0.02),
            0 4px 12px rgba(0, 0, 0, 0.03),
            inset 0 1px 0 rgba(255, 255, 255, 0.08);
        /* 移动端使用更简单的过渡 */
        transition:
            transform 0.3s ease-out,
            box-shadow 0.3s ease-out,
            background-color 0.3s ease-out;
    }
}

@media (max-width: 400px) {
    .card {
        padding: 0.4rem;
        min-height: 4rem;
    }
}

.card:hover {
    /* 增强的悬停效果 - 更平滑的变换 */
    transform: translateY(-12px) scale(1.03) rotateX(2deg);
    /* 多层次阴影，创造更强烈的悬浮感 */
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.04),
        0 16px 32px rgba(0, 0, 0, 0.06),
        0 24px 48px rgba(79, 134, 198, 0.12),
        0 32px 64px rgba(79, 134, 198, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(79, 134, 198, 0.1);
    /* 悬停时更突出的玻璃拟态效果 */
    background: rgba(212, 230, 241, 0.28);
    backdrop-filter: blur(16px) saturate(1.2) brightness(1.05);
    /* 增强边框光晕 */
    border: 1px solid rgba(255, 255, 255, 0.4);
}

.card:active {
    transform: translateY(-6px) scale(0.98) rotateX(1deg);
    /* 点击时使用更紧凑但仍有质感的阴影效果 */
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.06),
        0 8px 16px rgba(0, 0, 0, 0.04),
        0 12px 24px rgba(79, 134, 198, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    background: rgba(212, 230, 241, 0.32);
    backdrop-filter: blur(14px) saturate(1.15);
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.card::before {
    content: '';
    position: absolute;
    inset: 0;
    /* 增强的梯度光晕效果 */
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.1) 25%,
        rgba(79, 134, 198, 0.15) 50%,
        rgba(120, 162, 210, 0.12) 75%,
        rgba(42, 84, 133, 0) 100%
    );
    opacity: 0;
    /* 更流畅的光效过渡 */
    transition:
        opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
        transform 1.0s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translateX(-120%) rotate(-10deg);
    pointer-events: none;
    z-index: 1;
    border-radius: inherit;
}

.card:hover::before {
    opacity: 0.8;
    transform: translateX(120%) rotate(10deg);
}

/* 增强的卡片边缘发光效果 */
.card::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px;
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.6) 0%,
        rgba(79, 134, 198, 0.4) 30%,
        rgba(120, 162, 210, 0.3) 70%,
        rgba(42, 84, 133, 0.25) 100%
    );
    -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    opacity: 0;
    transition:
        opacity 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
        filter 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    pointer-events: none;
    filter: blur(0.5px);
}

.card:hover::after {
    opacity: 0.9;
    filter: blur(0px);
}

/* 移动端悬浮效果降级 */
@media (max-width: 767px) {
    .card::before {
        display: none;
    }

    .card:hover {
        /* 移动端使用更轻微的悬浮效果 */
        transform: translateY(-4px) scale(1.01);
        box-shadow:
            0 4px 8px rgba(0, 0, 0, 0.04),
            0 8px 16px rgba(0, 0, 0, 0.03),
            0 12px 24px rgba(79, 134, 198, 0.06);
        background: rgba(212, 230, 241, 0.22);
        backdrop-filter: blur(10px) saturate(1.1);
    }

    .card:active {
        transform: translateY(-2px) scale(0.99);
        transition: all 0.1s ease-out;
    }
}

/* 添加微妙的呼吸效果（仅桌面端） */
@media (min-width: 768px) {
    .card {
        animation: cardBreathing 6s ease-in-out infinite;
    }

    .card:hover {
        animation: none;
    }
}

@keyframes cardBreathing {
    0%, 100% {
        box-shadow:
            0 2px 8px rgba(0, 0, 0, 0.02),
            0 4px 16px rgba(0, 0, 0, 0.03),
            0 8px 32px rgba(79, 134, 198, 0.04),
            0 16px 64px rgba(79, 134, 198, 0.02),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }
    50% {
        box-shadow:
            0 3px 10px rgba(0, 0, 0, 0.025),
            0 6px 20px rgba(0, 0, 0, 0.035),
            0 12px 40px rgba(79, 134, 198, 0.05),
            0 20px 80px rgba(79, 134, 198, 0.025),
            inset 0 1px 0 rgba(255, 255, 255, 0.12);
    }
}

/* 当模态框打开时防止body滚动 */
.modal-open {
    overflow: hidden;
}

/* 增强的卡片点击涟漪效果 */
.card-ripple {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(
        circle,
        rgba(58, 110, 165, 0.4) 0%,
        rgba(79, 134, 198, 0.3) 30%,
        rgba(120, 162, 210, 0.2) 60%,
        rgba(255, 255, 255, 0.1) 100%
    );
    transform: scale(0);
    animation: enhancedRipple 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    pointer-events: none;
    will-change: transform, opacity;
    filter: blur(0.5px);
}

@keyframes enhancedRipple {
    0% {
        transform: scale(0);
        opacity: 1;
        filter: blur(0.5px);
    }
    50% {
        transform: scale(2);
        opacity: 0.6;
        filter: blur(0px);
    }
    100% {
        transform: scale(4.5);
        opacity: 0;
        filter: blur(1px);
    }
}

/* 状态标签样式 */
.status-badge {
    z-index: 10;
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 状态标签动画 */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 确保primary颜色可用 */
.bg-primary\/15 {
    background-color: rgba(58, 110, 165, 0.15);
}

.text-primary-dark {
    color: #2A5485;
}

/* 蓝色状态标签样式 */
.bg-blue-100 {
    background-color: rgba(219, 234, 254, 0.9);
}

.text-blue-800 {
    color: #1e40af;
}

/* 布局容器样式 */
#business-support, #geo-location, #data-analysis, #network-planning, #tutorials {
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
    min-height: 120px;
}

/* 在小屏幕上调整最大宽度以适应2列布局 */
@media (max-width: 639px) {
    #business-support, #geo-location, #data-analysis, #network-planning, #tutorials {
        max-width: 400px;
    }
}

/* 导航栏描述文字样式 */
header nav p {
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.3;
    max-width: 280px;
    transition: color 0.3s ease;
}

header nav p:hover {
    color: #4f86c6;
}

/* 响应式优化 - 在较小的桌面屏幕上调整描述文字 */
@media (min-width: 768px) and (max-width: 1024px) {
    header nav p {
        font-size: 0.8rem;
        max-width: 240px;
    }
}

/* 大屏幕上的优化 */
@media (min-width: 1280px) {
    header nav p {
        font-size: 0.9rem;
        max-width: 320px;
    }
}

/* 以下关键样式已内联到HTML中，这里仅作参考
body {
    font-family: 'Exo 2', 'Noto Sans SC', sans-serif;
    background-color: #d4e6f1;
}

.scroll-reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-reveal.is-visible {
    opacity: 1;
    transform: translateY(0);
}

.card {
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.01);
    background: rgba(212, 230, 241, 0.15);
    backdrop-filter: blur(6px);
    position: relative;
    overflow: hidden;
    padding: 1rem;
    border-radius: 0.5rem;
}

.card:hover {
    transform: translateY(-4px) scale(1.01);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.02), 0 8px 10px -6px rgba(0, 0, 0, 0.01);
    background: rgba(212, 230, 241, 0.25);
}

.card:active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 2px 8px -2px rgba(0, 0, 0, 0.1);
    transition: all 0.1s ease-out;
}

.card::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(120deg, rgba(42, 84, 133, 0) 30%, rgba(79, 134, 198, 0.4) 50%, rgba(42, 84, 133, 0) 70%);
    opacity: 0;
    transition: opacity 0.8s ease, transform 0.8s ease;
    transform: translateX(-100%);
    pointer-events: none;
    z-index: 1;
}

.card:hover::before {
    opacity: 1;
    transform: translateX(100%);
}

.card-ripple {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(58, 110, 165, 0.3);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.modal-open {
    overflow: hidden;
}
*/

/* 非关键增强样式 - 这些样式不影响首次内容绘制，可以延迟加载 */

/* 板块标题样式 - 现代化设计，调整为更小的字体尺寸 */
.section-header {
    position: relative;
    margin-bottom: 1.25rem;
}

.section-title {
    background: linear-gradient(135deg, #2A5485 0%, #4F86C6 50%, #3A6EA5 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #2A5485; /* 降级支持 */
    text-shadow: 0 2px 4px rgba(42, 84, 133, 0.1);
    letter-spacing: -0.02em;
    line-height: 1.2;
    position: relative;
    transition: all 0.3s ease;
}



/* 响应式标题优化 - 更小的字体尺寸 */
@media (max-width: 640px) {
    .section-header {
        margin-bottom: 1rem;
    }

    .section-title {
        font-size: 1.125rem !important; /* 18px */
        letter-spacing: -0.015em;
    }


}

@media (min-width: 640px) and (max-width: 1023px) {
    .section-title {
        font-size: 1.25rem !important; /* 20px */
        letter-spacing: -0.02em;
    }


}

@media (min-width: 1024px) {
    .section-title {
        font-size: 1.5rem !important; /* 24px */
        letter-spacing: -0.025em;
    }


}

/* 4列布局容器宽度限制 - 确保每行恰好显示4个卡片 */
#business-support, #geo-location, #data-analysis, #network-planning, #tutorials {
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
    min-height: 120px; /* 确保每个section有最小高度，避免加载时的空白 */
}

/* 在小屏幕上调整最大宽度以适应2列布局 */
@media (max-width: 639px) {
    #business-support, #geo-location, #data-analysis, #network-planning, #tutorials {
        max-width: 400px;
    }
}

/* 页面区域过渡增强 - 移除硬边界，创建平滑过渡 */
header {
    border-bottom: none;
    transition: background-color 0.3s ease;
}

/* 导航栏描述文字样式 */
header nav p {
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.3;
    max-width: 280px;
    transition: color 0.3s ease;
}

header nav p:hover {
    color: #4f86c6;
}

/* 响应式优化 - 在较小的桌面屏幕上调整描述文字 */
@media (min-width: 768px) and (max-width: 1024px) {
    header nav p {
        font-size: 0.8rem;
        max-width: 240px;
    }
}

/* 大屏幕上的优化 */
@media (min-width: 1280px) {
    header nav p {
        font-size: 0.9rem;
        max-width: 320px;
    }
}

main section {
    position: relative;
    transition: opacity 0.4s ease;
}

main section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 10%;
    right: 10%;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(226, 232, 240, 0.3), transparent);
    pointer-events: none;
}

main section:last-of-type::after {
    display: none;
}

/* 卡片样式增强 - 更细腻的阴影和过渡效果 */
.card {
    /* 升级阴影效果，使用多层阴影创造更精致的深度感 */
    box-shadow: 
        0 5px 15px rgba(0, 0, 0, 0.04),
        0 10px 30px rgba(0, 0, 0, 0.03),
        0 15px 45px rgba(79, 134, 198, 0.05);
    /* 改进过渡效果，使用更自然的缓动曲线 */
    transition: 
        transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1),
        box-shadow 0.4s cubic-bezier(0.16, 1, 0.3, 1),
        background-color 0.3s ease;
    /* 移除边框效果 */
    border: none;
    /* 更精致的玻璃特效背景色 */
    background: rgba(212, 230, 241, 0.25);
    /* 增强玻璃模糊效果 */
    backdrop-filter: blur(10px);
    /* 添加微弱的内部边框光晕 */
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.card:hover {
    /* 更精致的悬停效果 */
    transform: translateY(-8px) scale(1.02);
    /* 多层次阴影，创造更自然的提升感 */
    box-shadow: 
        0 10px 20px -2px rgba(0, 0, 0, 0.05),
        0 15px 30px -5px rgba(0, 0, 0, 0.04),
        0 20px 40px -8px rgba(79, 134, 198, 0.08);
    /* 悬停时更突出的玻璃效果 */
    background: rgba(212, 230, 241, 0.35);
    /* 加强内部边框光晕 */
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.card:active {
    transform: translateY(-4px) scale(0.99);
    /* 点击时使用更紧凑的阴影效果 */
    box-shadow: 
        0 5px 15px -2px rgba(0, 0, 0, 0.06),
        0 8px 20px -4px rgba(79, 134, 198, 0.1);
    transition: all 0.2s ease-out;
}

/* 桌面端卡片增强 - 增加内边距使卡片更宽，优化水平布局高度 */
@media (min-width: 640px) {
    .card {
        padding: 1rem;
        min-height: 6rem;
    }
}

/* 移动端优化样式 */
@media (max-width: 767px) {
    .card {
        /* 更合适的间距 */
        padding: 0.75rem;
        /* 更小的圆角 */
        border-radius: 0.375rem;
        /* 更轻的阴影 */
        box-shadow: 
            0 3px 8px rgba(0, 0, 0, 0.03),
            0 5px 15px rgba(0, 0, 0, 0.02);
        /* 更小的尺寸适合两列布局，优化水平布局高度 */
        min-height: 4.5rem;
        /* 移动端下的玻璃效果要更轻 */
        backdrop-filter: blur(6px);
        background: rgba(212, 230, 241, 0.2);
    }
    
    /* 移动端的激活状态 */
    .card.active {
        transform: scale(0.98);
        background: rgba(212, 230, 241, 0.25);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
        transition: all 0.15s ease-out;
    }
    
    /* 移动端下不显示过渡光效 */
    .card::before {
        display: none;
    }
    
    /* 调整移动端下的卡片标题大小 */
    .card h3 {
        font-size: 0.75rem;
        line-height: 1.2;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    /* 调整移动端下的卡片描述文本大小 */
    .card p {
        font-size: 0.7rem;
        line-height: 1.2;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        max-height: 2.4rem;
    }
    
    /* 调整移动端下的卡片图标大小 */
    .card .icon {
        font-size: 1.25rem;
        margin-bottom: 0.25rem;
    }
    
    /* 调整移动端下的状态标签 */
    .card .absolute.top-1 {
        font-size: 0.65rem;
        line-height: 1;
        padding: 0.1rem 0.25rem;
    }
}

/* 超小屏幕优化 (小于400px) */
@media (max-width: 400px) {
    .card {
        padding: 0.4rem;
        min-height: 4rem;
    }
    
    .card h3 {
        font-size: 0.7rem;
        margin-bottom: 0.125rem;
    }
    
    .card p {
        font-size: 0.65rem;
        -webkit-line-clamp: 2;
    }
    
    .card .icon {
        font-size: 1.125rem;
        margin-bottom: 0.125rem;
    }
    
    .card .absolute.top-1 {
        font-size: 0.6rem;
        padding: 0.05rem 0.2rem;
    }
}

/* 改进卡片光晕效果 */
.card::before {
    /* 更微妙的梯度光晕效果 */
    background: linear-gradient(
        120deg, 
        rgba(42, 84, 133, 0) 20%, 
        rgba(79, 134, 198, 0.35) 45%, 
        rgba(120, 162, 210, 0.3) 55%, 
        rgba(42, 84, 133, 0) 80%
    );
    /* 更平滑的过渡 */
    transition: opacity 1s ease, transform 1.2s cubic-bezier(0.165, 0.84, 0.44, 1);
}

/* 卡片内部元素增强 */
.card .icon {
    /* 增强图标过渡效果 */
    transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275), color 0.3s ease;
    /* 添加微妙的文本阴影 */
    text-shadow: 0 2px 6px rgba(79, 134, 198, 0.1);
}

.card:hover .icon {
    transform: scale(1.15) translateY(-2px);
}

/* 卡片标题增强 */
.card h3 {
    transition: color 0.3s ease, transform 0.3s ease;
}

.card:hover h3 {
    transform: translateX(2px);
}

/* 卡片描述文本增强 */
.card p {
    transition: color 0.3s ease;
}

.card:hover p {
    color: #4a4f59;
}

/* 卡片上的自定义渐变光晕效果 */
.card-glow::before {
    content: none;
    /* 禁用原来的渐变光晕效果 */
}

.group:hover .card-glow::before {
    opacity: 0;
}

/* 引用样式装饰 */
.card::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px;
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.5) 0%,
        rgba(79, 134, 198, 0.3) 50%,
        rgba(42, 84, 133, 0.2) 100%
    );
    -webkit-mask: 
        linear-gradient(#fff 0 0) content-box, 
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.4s ease;
    pointer-events: none;
}

.card:hover::after {
    opacity: 1;
}

/* 添加额外的视觉效果增强 */
.card .icon {
    transition: transform 0.3s ease;
}

.card:hover .icon {
    transform: scale(1.1);
}

/* 模态框额外样式 */
#modal-panel {
    max-height: 90vh;
    overflow-y: auto;
}

/* 其他增强样式 */
::selection {
    background-color: rgba(58, 110, 165, 0.2);
    color: #2A5485;
}

/* 链接悬停效果 */
a:not(.card) {
    transition: color 0.2s ease;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: rgba(212, 230, 241, 0.5);
}

::-webkit-scrollbar-thumb {
    background: rgba(58, 110, 165, 0.3);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(58, 110, 165, 0.5);
} 