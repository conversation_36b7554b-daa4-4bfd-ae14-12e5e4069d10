/**
 * 访问统计模块
 * 负责集成和管理不蒜子访问统计功能
 * 版本: v3.0.0
 * 变更记录:
 * - v3.0.0 (2025-01-27): 新增本地环境检测 - 在开发环境下自动禁用统计功能，提升开发体验
 * - v2.0.0 (2025-01-27): 重大简化 - 移除过度复杂的错误处理和重试机制，提升代码可维护性
 * - v1.1.0 (2025-07-27): 优化脚本检测逻辑，改进数据加载检测机制
 * - v1.0.0 (2025-07-27): 初始版本，集成不蒜子访问统计功能
 */

class Statistics {
    constructor() {
        this.config = window.AppConfig?.statistics || {};
        this.initialized = false;
        this.container = null;
        this.isLocalEnvironment = this.detectLocalEnvironment();
    }

    /**
     * 检测是否为本地开发环境
     * @returns {boolean} 如果是本地环境返回true，否则返回false
     */
    detectLocalEnvironment() {
        const location = window.location;
        const hostname = location.hostname;
        const protocol = location.protocol;
        const port = location.port;

        // 检测本地主机名
        const localHostnames = ['localhost', '127.0.0.1', '0.0.0.0'];
        if (localHostnames.includes(hostname)) {
            return true;
        }

        // 检测file协议
        if (protocol === 'file:') {
            return true;
        }

        // 检测常见开发端口
        const developmentPorts = ['3000', '8000', '8080', '5000', '4000', '9000', '3001', '8001'];
        if (port && developmentPorts.includes(port)) {
            return true;
        }

        // 检测本地IP地址范围
        if (hostname.startsWith('192.168.') || hostname.startsWith('10.') || hostname.startsWith('172.')) {
            return true;
        }

        return false;
    }

    /**
     * 初始化统计功能
     */
    init() {
        if (this.initialized) return;

        // 在本地环境下禁用统计功能
        if (this.isLocalEnvironment) {
            this.logLocalEnvironmentMessage();
            this.initialized = true;
            return;
        }

        this.loadBusuanziScript();
        this.createContainer();
        this.waitForData();
        this.initialized = true;
    }

    /**
     * 动态加载不蒜子统计脚本
     */
    loadBusuanziScript() {
        // 检查脚本是否已经加载
        if (document.querySelector('script[src*="busuanzi"]')) {
            return;
        }

        const script = document.createElement('script');
        script.async = true;
        script.src = '//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js';
        script.onerror = () => {
            console.warn('[Statistics] 不蒜子脚本加载失败，统计功能将不可用');
        };
        document.head.appendChild(script);
    }

    /**
     * 在控制台输出本地环境提示信息
     */
    logLocalEnvironmentMessage() {
        const location = window.location;
        console.info(
            '%c[Statistics] 统计功能已在开发环境下禁用',
            'color: #4F86C6; font-weight: bold;',
            `\n当前环境: ${location.protocol}//${location.hostname}${location.port ? ':' + location.port : ''}`,
            '\n统计功能将在生产环境中正常工作'
        );
    }

    /**
     * 创建统计显示容器
     */
    createContainer() {
        const footer = document.querySelector('footer');
        if (!footer) return;

        this.container = document.createElement('div');
        this.container.id = 'site-statistics';
        this.container.className = 'text-sm text-gray-500 mt-2';
        this.container.style.opacity = '0';
        this.container.innerHTML = this.buildStatisticsHTML();

        const copyright = footer.querySelector('p');
        if (copyright) {
            footer.insertBefore(this.container, copyright);
        } else {
            footer.appendChild(this.container);
        }
    }

    /**
     * 构建统计HTML
     */
    buildStatisticsHTML() {
        return `
            <span id="busuanzi_container_site_pv">
                本站总访问量 <span id="busuanzi_value_site_pv" class="font-medium text-primary">--</span> 次
            </span>
            |
            <span id="busuanzi_container_site_uv">
                独立访客数 <span id="busuanzi_value_site_uv" class="font-medium text-primary">--</span> 次
            </span>
        `;
    }

    /**
     * 等待数据加载
     */
    waitForData() {
        const checkData = () => {
            const sitePV = document.getElementById('busuanzi_value_site_pv');
            const siteUV = document.getElementById('busuanzi_value_site_uv');

            if ((sitePV && sitePV.textContent !== '--') ||
                (siteUV && siteUV.textContent !== '--')) {
                this.showStatistics();
            } else {
                setTimeout(checkData, 1000);
            }
        };

        setTimeout(checkData, 2000); // 延迟2秒开始检查
    }

    /**
     * 显示统计信息
     */
    showStatistics() {
        if (this.container) {
            this.container.style.opacity = '1';
        }
    }

    /**
     * 检查统计功能是否可用
     * @returns {boolean} 如果统计功能可用返回true，否则返回false
     */
    isEnabled() {
        return !this.isLocalEnvironment;
    }

    /**
     * 获取当前环境信息
     * @returns {Object} 包含环境信息的对象
     */
    getEnvironmentInfo() {
        const location = window.location;
        return {
            isLocal: this.isLocalEnvironment,
            hostname: location.hostname,
            protocol: location.protocol,
            port: location.port,
            url: location.href
        };
    }

    /**
     * 销毁统计功能
     */
    destroy() {
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
        this.initialized = false;
        this.container = null;
    }
}

// 导出统计模块
window.Statistics = Statistics;
